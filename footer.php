<div id="chat-button" class="chat-button" onclick="toggleChat()">
    <i class="fa fa-comments"></i> 
</div>


<footer id="footer" class="footer">
    <!-- Footer Top -->
    <div class="footer-top">
        <div class="container">
            <div class="row">
                
                <!-- Logo & Description -->
                <div class="col-lg-2 col-md-6 col-12">
                    <div class="single-footer">
                        <div class="footer-logo">
                            <img src="img/footer_logo.png" alt="Logo" class="img-fluid">
                        </div>
                     
                    </div>
                </div>
                 <div class="col-lg-1 col-md-6 col-12">
                     
                 </div>
                <div class="col-lg-4 col-md-6 col-12">
                    <div class="single-footer">
                        <h2>Address</h2>
                        <p>
                            2nd Floor,
                            Chakkampalliyalil, <br>
                            Angadipuram,  
                            Malappuram, 
                            Kerala, India
                        </p>
                           <ul class="social">
                            <li><a href="https://www.facebook.com/profile.php?id=61564978560349"><i class="icofont-facebook"></i></a></li>
                            <li><a href="https://www.instagram.com/simplymedlearning/"><i class="icofont-instagram"></i></a></li>
                            <li><a href="https://www.linkedin.com/company/simplymed-learning/"><i class="icofont-linkedin"></i></a></li>
                        </ul>
                    </div>
                </div>
                <div class="col-lg-1 col-md-6 col-12">
                     
                 </div>
                

           
                <div class="col-lg-4 col-md-6 col-12">
                    <div class="single-footer">
                        <h2>Newsletter</h2>
                        <p>Subscribe to our newsletter to get all our news in your inbox.</p>
                        <form action="#" method="get" class="newsletter-inner" target="_blank">
                            <input name="email" placeholder="Email Address" class="common-input" type="email" required
                                   onfocus="this.placeholder = ''"
                                   onblur="this.placeholder = 'Your email address'">
                            <button class="button"><i class="icofont-paper-plane"></i></button>
                        </form>
                    </div>
                </div>

            </div>
        </div>
    </div>

    <!-- Copyright -->
    <div class="copyright">
        <div class="container">
            <div class="row">
                <div class="col-lg-12 col-12">
                    <div class="copyright-content">
                        <p>© Copyright 2025 | All Rights Reserved by 
                            <a href="#" target="_blank">SimplyMED</a>
                        </p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</footer>

<div id="chatbox" class="chatbox" style="display: none;">
    <div class="chatbox-header">
        <img src="img/shutterstock_251320138.jpg" alt="Med Mentor" class="faculty-photo">
        <h3 class="faculty-name">Med Mentor</h3>
        <p class="faculty-title"></p>
        <button onclick="toggleChat()" class="close-btn">&times;</button>
    </div>
    <div class="chat-messages" id="chat-messages">
    
    </div>
    <div class="chat-input">
        <input type="text" id="user-message" placeholder="Type your message...">
        <button onclick="sendMessage()">Send</button>
    </div>
</div>

<script src="js/jquery.min.js"></script>
<script src="js/jquery-migrate-3.0.0.js"></script>
<script src="js/jquery-ui.min.js"></script>
<script src="js/easing.js"></script>
<script src="js/colors.js"></script>
<script src="js/popper.min.js"></script>
<script src="js/bootstrap-datepicker.js"></script>
<script src="js/jquery.nav.js"></script>
<script src="js/slicknav.min.js"></script>
<script src="js/niceselect.js"></script>
<script src="js/tilt.jquery.min.js"></script>
<script src="js/owl-carousel.js"></script>
<script src="js/jquery.counterup.min.js"></script>
<script src="js/steller.js"></script>
<script src="js/wow.min.js"></script>
<script src="js/jquery.magnific-popup.min.js"></script>
<script src="http://cdnjs.cloudflare.com/ajax/libs/waypoints/2.0.3/waypoints.min.js"></script>
<script src="js/bootstrap.min.js"></script>
<script src="js/main.js"></script>

<!-- Chatbot Script -->
<script>
window.onload = function () {
    const chatbox = document.getElementById('chatbox');
    chatbox.style.display = "flex";

    const messagesContainer = document.getElementById('chat-messages');
    const botMessage = document.createElement('div');
    botMessage.textContent = "How can I assist you today?";
    botMessage.style.textAlign = 'left';
    botMessage.style.margin = '5px 0';
    botMessage.style.color = '#555';
    messagesContainer.appendChild(botMessage);
};

function toggleChat() {
    const chatbox = document.getElementById('chatbox');
    chatbox.style.display = chatbox.style.display === "none" || chatbox.style.display === "" ? "flex" : "none";
}

function sendMessage() {
    const messageInput = document.getElementById('user-message');
    const messagesContainer = document.getElementById('chat-messages');

    if (messageInput.value.trim() !== "") {
        const userMessage = document.createElement('div');
        userMessage.textContent = messageInput.value;
        userMessage.style.textAlign = 'right';
        userMessage.style.margin = '5px 0';
        messagesContainer.appendChild(userMessage);

        setTimeout(() => {
            const botMessage = document.createElement('div');
            botMessage.textContent = "Thank you for reaching out. How can I assist you?";
            botMessage.style.textAlign = 'left';
            botMessage.style.margin = '5px 0';
            botMessage.style.color = '#555';
            messagesContainer.appendChild(botMessage);
            messagesContainer.scrollTop = messagesContainer.scrollHeight;
        }, 500);

        messageInput.value = "";
    }
}
</script>

<!-- Owl Carousel Init -->
<script>
$(document).ready(function () {
    $(".course-carousel").owlCarousel({
        loop: true,
        margin: 20,
        nav: false,
        dots: false,
        autoplay: true,
        autoplayTimeout: 3000,
        responsive: {
            0: { items: 1 },
            576: { items: 2 },
            768: { items: 3 },
            992: { items: 4 },
            1200: { items: 4 }
        }
    });
});
</script>
   </body>
</html>
